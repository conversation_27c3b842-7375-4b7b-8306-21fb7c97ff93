import React, { createContext, useContext, useState, ReactNode } from 'react';

interface OnboardingData {
  realName: string;
  artistName: string;
  profilePhoto?: File | null;
  profilePhotoUrl?: string; // Local preview URL
  profilePhotoS3Url?: string; // S3 uploaded URL
  bio?: string;
  roles?: string[];
  subRoles?: string[];
  genres?: string[];
  artistProfileType?: 'search' | 'url';
  artistProfileSearch?: string;
  artistProfileUrl?: string;
  noArtistProfile?: boolean;
  followArtists?: string[];
  // Social links for new step
  spotifyLink?: string;
  appleMusicLink?: string;
  youtubeLink?: string;
  instagramLink?: string;
  linkedinLink?: string;
  otherLink?: string;
}

interface OnboardingContextType {
  step: number;
  setStep: (step: number) => void;
  totalSteps: number;
  data: OnboardingData;
  setData: (data: Partial<OnboardingData>) => void;
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

export const OnboardingProvider = ({ children }: { children: ReactNode }) => {
  const [step, setStep] = useState(1);
  const totalSteps = 8; // Updated for new step
  const [data, setDataState] = useState<OnboardingData>({
    realName: '',
    artistName: '',
    profilePhoto: null,
    profilePhotoUrl: '',
    profilePhotoS3Url: '',
    bio: '',
    roles: [],
    subRoles: [],
    spotifyLink: '',
    appleMusicLink: '',
    youtubeLink: '',
    instagramLink: '',
    linkedinLink: '',
    otherLink: '',
  });

  const setData = (newData: Partial<OnboardingData>) => {
    setDataState(prev => ({ ...prev, ...newData }));
  };

  return (
    <OnboardingContext.Provider value={{ step, setStep, totalSteps, data, setData }}>
      {children}
    </OnboardingContext.Provider>
  );
};

export const useOnboarding = () => {
  const context = useContext(OnboardingContext);
  if (!context) throw new Error('useOnboarding must be used within OnboardingProvider');
  return context;
};
