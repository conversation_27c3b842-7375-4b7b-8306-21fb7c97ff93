# Remix Page Theming System

## Overview

The remix page uses a **page-scoped theming system** that:
- ✅ Only affects the `/remix` route
- ✅ Uses official Tailwind/shadcn naming conventions (`text-primary`, `bg-secondary`, etc.)
- ✅ Uses light theme only (no dark mode or system theme switching)
- ✅ Can be easily customized from a single config file
- ✅ No custom class names or inline CSS

## How It Works

### 1. Theme Configuration (`theme-config.ts`)
All colors are defined in HSL format for both light and dark modes:

```typescript
export const remixThemeConfig = {
  light: {
    primary: {
      DEFAULT: "330 81% 67%", // #f472b6 - Main pink color
      foreground: "0 0% 100%", // White text on pink
    },
    // ... more colors
  },
  dark: {
    primary: {
      DEFAULT: "330 81% 67%", // Same pink, works well on dark
      foreground: "222.2 84% 4.9%", // Dark text on pink
    },
    // ... more colors
  },
}
```

### 2. Theme Provider (`remix-theme-provider.tsx`)
- Forces light theme mode for the remix flow
- Applies the remix theme colors via CSS custom properties
- Scopes the theme to only affect elements within the remix page

### 3. Usage in Components
Use standard Tailwind/shadcn classes:

```jsx
// ✅ Correct - uses official classes
<h1 className="text-primary">ATTENTION</h1>
<div className="bg-card border border-border">
<Button className="bg-primary text-primary-foreground">
```

## Available Theme Colors

### Primary Colors
- `text-primary` / `bg-primary` - Main pink color (#f472b6)
- `text-primary-foreground` - Text color on primary background

### Secondary Colors  
- `text-secondary` / `bg-secondary` - Light gray backgrounds
- `text-secondary-foreground` - Text on secondary backgrounds

### Muted Colors
- `text-muted` / `bg-muted` - Very light gray for subtle elements
- `text-muted-foreground` - Medium gray text

### Accent Colors
- `text-accent` / `bg-accent` - Orange accent color
- `text-accent-foreground` - Text on accent backgrounds

### UI Elements
- `bg-background` - Page background
- `text-foreground` - Main text color
- `bg-card` / `text-card-foreground` - Card backgrounds and text
- `border-border` - Border color
- `bg-input` - Input field backgrounds
- `ring-ring` - Focus ring color

## Customizing Colors

### To Change the Pink Color:
Edit `src/app/remix/theme-config.ts`:

```typescript
primary: {
  DEFAULT: "280 100% 70%", // Change to purple
  foreground: "0 0% 100%",
},
```

### To Add New Colors:
1. Add to the config:
```typescript
brand: {
  DEFAULT: "200 100% 50%", // Blue
  foreground: "0 0% 100%",
},
```

2. Update the provider to include the new color:
```typescript
vars['--brand'] = `hsl(${themeColors.brand.DEFAULT})`;
vars['--brand-foreground'] = `hsl(${themeColors.brand.foreground})`;
```

3. Use in components:
```jsx
<div className="bg-brand text-brand-foreground">
```

## Theme Mode

The remix flow uses light theme only:
- Always uses `remixThemeConfig.light`
- No dark mode or system theme switching
- Consistent light theme experience across all remix pages

## Benefits

1. **Centralized Control**: All colors in one config file
2. **Official Classes**: Uses standard Tailwind/shadcn naming
3. **Automatic Dark Mode**: No manual theme switching needed
4. **Page Scoped**: Only affects the remix page
5. **Type Safe**: Full TypeScript support
6. **Easy Maintenance**: Change colors without touching components
