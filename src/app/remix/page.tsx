'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Play, Pause, Download, Send, Settings, Upload, Music, LogOut, Loader2 } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';


import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import { FileUploadModal } from '@/components/remix/file-upload-modal';
import { UploadFile } from '@/lib/file-upload-utils';

import { useMusicPlayer } from '@/contexts/music-player-context/music-player-context';
import { useRemixModalTheme } from './utils/modal-theme';
import { useAuth } from '@/contexts/auth/auth-context';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

function RemixPageContent() {
  const searchParams = useSearchParams()
  const isOnboarded = searchParams.get('onboarding')
  const [isAgreed, setIsAgreed] = useState(false);
  const [showUploadModal, setShowUploadModal] = useState(isOnboarded === 'done');
  const [showDisclaimerModal, setShowDisclaimerModal] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadMessage, setDownloadMessage] = useState('');
  const [, setUploadedFiles] = useState<UploadFile[]>([]);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const router = useRouter();
  const { playSong, currentSong, isPlaying, togglePlay } = useMusicPlayer();
  const { isAuthenticated, signOut } = useAuth();

  // Check for pending actions when user returns from login/onboarding
  useEffect(() => {
    if (isAuthenticated && !isLoggingOut) {
      const pendingAction = localStorage.getItem('remix-pending-action');
      const hasCompletedOnboarding = localStorage.getItem('remix-onboarding') === 'done';

      if (pendingAction === 'upload-modal' && hasCompletedOnboarding) {
        // Clear the pending action
        localStorage.removeItem('remix-pending-action');
        // Open the upload modal
        setShowUploadModal(true);
      }
    }
  }, [isAuthenticated, isLoggingOut]);

  // Define the specific song for the remix contest
  const remixSong = {
    id: "remix-attention-kesha",
    title: "ATTENTION! (Open Verse)",
    artist: "Kesha",
    album: "Open Verse Contest",
    albumArt: "/remix/main-image.png", // Using the contest image
    duration: 180, // Approximate duration - will be updated when loaded
    audioSrc: "https://d1jds31zoh6k0w.cloudfront.net/ATTENTION!+open+verse2+134BPM.wav",
    credits: {
      producer: "Kesha",
      writer: "Kesha",
      engineer: "Unknown",
    },
  };

  const handlePlay = () => {
    if (currentSong?.id === remixSong.id) {
      togglePlay();
    } else {
      // Play the specific remix song and disable navigation and expand
      playSong(remixSong, { disableNavigation: true, disableExpand: true });
    }
  };

  const handleDownload = async () => {
    setIsDownloading(true);

    try {
      // Method 1: Use our API proxy to download the file
      const proxyUrl = `/api/download-audio?url=${encodeURIComponent(remixSong.audioSrc)}`;
      const response = await fetch(proxyUrl);

      if (response.ok) {
        const blob = await response.blob();

        // Create download link with blob
        const blobUrl = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = 'ATTENTION_Open_Verse.wav';
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up blob URL
        setTimeout(() => URL.revokeObjectURL(blobUrl), 100);

        setDownloadMessage('Download started successfully!');
        setIsDownloading(false);

        // Clear message after 3 seconds
        setTimeout(() => setDownloadMessage(''), 3000);
        return;
      }
    } catch {
      console.log('API proxy download failed, trying direct method');
    }

    try {
      // Method 2: Try direct download with proper headers
      const response = await fetch(remixSong.audioSrc, {
        method: 'GET',
        headers: {
          'Accept': 'audio/wav,audio/*,*/*',
        },
      });

      if (response.ok) {
        const blob = await response.blob();

        // Create download link with blob
        const blobUrl = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = 'ATTENTION_Open_Verse.wav';
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up blob URL
        setTimeout(() => URL.revokeObjectURL(blobUrl), 100);

        setIsDownloading(false);
        return;
      }
    } catch {
      console.log('Direct fetch download failed, using fallback');
    }

    // Method 3: Fallback to direct link download
    const link = document.createElement('a');
    link.href = remixSong.audioSrc;
    link.download = 'ATTENTION_Open_Verse.wav';
    link.style.display = 'none';

    // Force download behavior
    link.setAttribute('download', 'ATTENTION_Open_Verse.wav');
    link.setAttribute('target', '_self');

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Reset loading state after a short delay
    setTimeout(() => {
      setIsDownloading(false);
    }, 1500);
  };

  const handleSubmit = async () => {
    if (!isAgreed) return;

    // Check if user is authenticated
    if (!isAuthenticated) {
      // Store the intent to open upload modal after login/onboarding
      localStorage.setItem('remix-pending-action', 'upload-modal');
      // Redirect to login page
      router.push('/login');
      return;
    }

    // Check if user has completed remix onboarding
    const hasCompletedOnboarding = localStorage.getItem('remix-onboarding') === 'done';

    if (!hasCompletedOnboarding) {
      // Store the intent to open upload modal after onboarding
      localStorage.setItem('remix-pending-action', 'upload-modal');
      // Redirect to onboarding first
      router.push('/remix/onboarding');
    } else {
      // User is authenticated and onboarded, show submit modal
      setShowUploadModal(true);
    }
  };

  const handleUploadComplete = (files: UploadFile[]) => {
    setUploadedFiles(files);
    console.log('Files uploaded:', files);
    // Don't close the modal immediately - let the user see the success screen
    // The modal will be closed when the user clicks outside or presses escape
    // Or we can add a "Done" button in the success screen
  };

  const handleCloseModal = () => {
    setShowUploadModal(false);
  };

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);
      await signOut();
      // Clear any remix-specific data
      localStorage.removeItem('remix-pending-action');
      // Redirect to remix page (public)
      router.push('/remix');
    } catch (error) {
      console.error('Logout error:', error);
      setIsLoggingOut(false);
    }
  };

  // Disclaimer Modal Component
    const { cssVariables, themeMode } = useRemixModalTheme();
  
  const DisclaimerModal = () => (
    <Dialog open={showDisclaimerModal} onOpenChange={setShowDisclaimerModal}>
      <DialogContent className="max-w-[calc(100%-2rem)] sm:max-w-2xl max-h-[90vh] overflow-hidden p-0"
       style={cssVariables}
        data-theme={themeMode}>
        {/* Scrollable Content */}
        <div className="overflow-y-auto max-h-[80vh] px-6 md:px-8 py-8">
          <div className="max-w-2xl mx-auto space-y-6">

            {/* Title */}
            <div className="space-y-4">
              <h1 className="text-primary text-xl font-bold">
                Kesha &quot;Attention&quot; Remix Challenge: Official Disclaimer
              </h1>
              <p className="text-base font-inter text-card-foreground leading-relaxed">
                Please read this disclaimer carefully before participating in the Kesha &quot;Attention&quot; Remix Challenge (the &quot;Challenge&quot;). By submitting a remix, you agree to be bound by the terms and conditions outlined below.
              </p>
            </div>

            {/* Section 1 */}
            <div className="space-y-4">
              <h2 className="text-base font-inter font-semibold text-card-foreground leading-relaxed">
                1. No Grant of Rights for Submission
              </h2>
              <p className="text-base font-inter text-card-foreground leading-relaxed">
                By submitting your remix to the Challenge, you are not granting, Kesha Records or SMASH Music, license to your remix. If Kesha chooses your remix, Kesha Records will negotiate terms of the release in good faith.
              </p>
            </div>

            {/* Section 2 */}
            <div className="space-y-4">
              <h2 className="text-base font-inter font-semibold text-card-foreground leading-relaxed">
                2. Originality and Clearances
              </h2>
              <p className="text-base font-inter text-card-foreground leading-relaxed">
                You represent and warrant that your remix is an original work created solely by you, and that it does not infringe upon the copyrights, trademarks, privacy rights, publicity rights, or any other intellectual property or legal rights of any third party.
              </p>
            </div>

            {/* Section 3 */}
            <div className="space-y-4">
              <h2 className="text-base font-inter font-semibold text-card-foreground leading-relaxed">
                3. No Compensation for Submission
              </h2>
              <p className="text-base font-inter text-card-foreground leading-relaxed">
                Except for the potential opportunity described in Section 4, you acknowledge and agree that you will not receive any financial compensation or other remuneration for your submission to the Challenge or for the Kesha Parties&apos; use of your remix as described in Section 1.
              </p>
            </div>

            {/* Section 4 */}
            <div className="space-y-4">
              <h2 className="text-base font-inter font-semibold text-card-foreground leading-relaxed">
                4. Potential Official Release
              </h2>
              <p className="text-base font-inter text-card-foreground leading-relaxed">
                Kesha will review submitted remixes and may, at her sole discretion, select one remix for potential official release via Kesha Records. If your remix is selected, the terms of such official release, including but not limited to ownership, royalties, and all other commercial considerations, will be negotiated in good faith between you and Kesha Records. There is no guarantee that a mutually agreeable agreement will be reached, and Kesha Records reserves the right to withdraw any offer of release at any time prior to the execution of a definitive agreement.
              </p>
            </div>

            {/* Section 5 */}
            <div className="space-y-4">
              <h2 className="text-base font-inter font-semibold text-card-foreground leading-relaxed">
                5. Ownership of Stems
              </h2>
              <p className="text-base font-inter text-card-foreground leading-relaxed">
                The provided music stems for &quot;Attention&quot; remain the sole property of Kesha and/or her licensors. Your use of these stems is strictly limited to the creation of your remix for this Challenge and for no other purpose. You may not distribute, sell, or otherwise exploit the individual stems.
              </p>
            </div>

            {/* Section 6 */}
            <div className="space-y-4">
              <h2 className="text-base font-inter font-semibold text-card-foreground leading-relaxed">
                6. No Obligation to Release
              </h2>
              <p className="text-base font-inter text-card-foreground leading-relaxed">
                Kesha and Kesha Records are under no obligation to release any remix, even if one is selected. The decision to officially release a remix rests solely with Kesha Records.
              </p>
            </div>

            {/* Section 7 */}
            <div className="space-y-4">
              <h2 className="text-base font-inter font-semibold text-card-foreground leading-relaxed">
                7. Indemnification
              </h2>
              <p className="text-base font-inter text-card-foreground leading-relaxed">
                You agree to indemnify, defend, and hold harmless Kesha Parties from and against any and all claims, damages, liabilities, costs, and expenses (including reasonable attorneys&apos; fees) arising out of or in connection with your breach of any representation, warranty, or obligation under this disclaimer, or your participation in the Challenge.
              </p>
            </div>

            {/* Section 8 */}
            <div className="space-y-4">
              <h2 className="text-base font-inter font-semibold text-card-foreground leading-relaxed">
                8. Governing Law
              </h2>
              <p className="text-base font-inter text-card-foreground leading-relaxed">
                This disclaimer shall be governed by and construed in accordance with the laws of the State of California, without regard to its conflict of law principles.
              </p>
            </div>

            {/* Section 9 with Checkbox */}
            <div className="space-y-6">
              <div className="space-y-4">
                <h2 className="text-base font-inter font-semibold text-card-foreground leading-relaxed">
                  9. Modifications
                </h2>
                <p className="text-base font-inter text-card-foreground leading-relaxed">
                  Kesha reserves the right to modify or cancel the Challenge or these terms at any time, in her sole discretion, without prior notice.
                </p>
              </div>

              {/* Agreement Checkbox */}
              {/* <div className="flex items-start gap-3 pt-4">
                <Checkbox
                  id="disclaimer-agreement"
                  checked={disclaimerAgreed}
                  onCheckedChange={(checked) => setDisclaimerAgreed(checked as boolean)}
                  className="mt-1 w-5 h-5 border-primary data-[state=checked]:bg-primary data-[state=checked]:border-primary data-[state=checked]:text-primary-foreground"
                />
                <label htmlFor="disclaimer-agreement" className="text-base text-card-foreground cursor-pointer font-semibold font-lato leading-relaxed">
                  By submitting your remix, you acknowledge that you have read, understood, and agree to this disclaimer.
                </label>
              </div> */}
            </div>
          </div>
        </div>

        {/* Footer */}
        {/* <div className="border-t border-border px-6 md:px-8 py-6">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            <p className="text-foreground text-lg font-semibold font-lato">By Smash Music</p>
            <div className="flex gap-4">
              <Button
                onClick={handleDisclaimerCancel}
                variant="outline"
                className="px-6 py-2"
              >
                Cancel
              </Button>
              <Button
                onClick={handleDisclaimerAccept}
                disabled={!disclaimerAgreed}
                className="px-6 py-2 bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50"
              >
                Accept & Continue
              </Button>
            </div>
          </div>
        </div> */}
      </DialogContent>
    </Dialog>
  );

  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Fixed Header */}
   {isAuthenticated && (
        <header className="sticky top-0 z-50 bg-background flex items-center justify-between px-4 md:px-8 lg:px-16 py-4 border-b border-border">
          <div className="flex items-end gap-1">
            <div className="w-28 h-5 relative">
              <Image src="/SMASH-(full)-logo.png" alt="SMASH" fill className="object-contain" />
            </div>
          </div>
          <div className="flex items-center gap-4">
            {/* <button className="w-6 h-6 flex items-center justify-center">
              <Bell className="w-5 h-5 text-foreground" strokeWidth={1.5} />
            </button> */}

            {/* Settings Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button className="w-6 h-6 flex items-center justify-center hover:bg-muted rounded-sm transition-colors">
                  <Settings className="w-6 h-6 text-foreground" strokeWidth={1.5} />
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={handleLogout} className="text-destructive focus:text-destructive">
                  <LogOut className="w-4 h-4 mr-2" />
                  Logout
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <Avatar className="w-10 h-10">
              <AvatarImage src="/remix/user-avatar.png" alt="User" />
              <AvatarFallback className="bg-background text-foreground font-medium">U</AvatarFallback>
            </Avatar>
          </div>
        </header>
      )}

      {/* Scrollable Main Content */}
      <main className="flex-1 overflow-y-auto bg-background relative">
        {/* Background Geometric Elements - Positioned relative to content */}
        <div className="absolute inset-0 pointer-events-none overflow-hidden" aria-hidden="true">
          {/* Desktop background images */}
          <div className="hidden lg:block">
            {/* Element 1: Top left area */}
            <div className="absolute top-[180px] left-[120px] w-[134px] h-[153px]">
              <Image src="/remix/bg-image-1-3ba70c.png" alt="" fill className="object-cover" />
            </div>
            {/* Element 2: Top right area */}
            <div className="absolute top-[160px] right-[120px] w-[103px] h-[114px]">
              <Image src="/remix/bg-image-2-24c5d3.png" alt="" fill className="object-cover" />
            </div>
            {/* Element 3: Left middle */}
            <div className="absolute top-[480px] left-[180px] w-[139px] h-[159px]">
              <Image src="/remix/bg-image-3-645d1a.png" alt="" fill className="object-cover" />
            </div>
            {/* Element 4: Bottom right */}
            <div className="absolute top-[620px] right-[80px] w-[131px] h-[91px]">
              <Image src="/remix/bg-image-4-41e4b7.png" alt="" fill className="object-cover" />
            </div>
            {/* Element 5: Right middle */}
            <div className="absolute top-[320px] right-[250px] w-[112px] h-[96px]">
              <Image src="/remix/bg-image-5-786eb3.png" alt="" fill className="object-cover" />
            </div>
            {/* Element 6: Far left */}
            <div className="absolute top-[400px] left-[-20px] w-[165px] h-[105px]">
              <Image src="/remix/bg-image-7-13131d.png" alt="" fill className="object-cover" />
            </div>
          </div>

          {/* Mobile background images - positioned as per mobile Figma */}
          <div className="block lg:hidden">
            {/* Mobile Element 1: Top left */}
            <div className="absolute top-[200px] left-[42px] w-[66px] h-[76px]">
              <Image src="/remix/bg-image-1-3ba70c.png" alt="" fill className="object-cover" />
            </div>
            {/* Mobile Element 2: Top right */}
            <div className="absolute top-[217px] right-[27px] w-[49px] h-[54px]">
              <Image src="/remix/bg-image-2-24c5d3.png" alt="" fill className="object-cover" />
            </div>
            {/* Mobile Element 3: Left middle */}
            <div className="absolute top-[389px] left-[1px] w-[68px] h-[77px]">
              <Image src="/remix/bg-image-3-645d1a.png" alt="" fill className="object-cover" />
            </div>
            {/* Mobile Element 4: Right middle */}
            <div className="absolute top-[440px] right-[1px] w-[54px] h-[46px]">
              <Image src="/remix/bg-image-5-786eb3.png" alt="" fill className="object-cover" />
            </div>
            {/* Mobile Element 5: Bottom left */}
            <div className="absolute top-[312px] left-[-24px] w-[82px] h-[52px]">
              <Image src="/remix/bg-image-7-13131d.png" alt="" fill className="object-cover" />
            </div>
            {/* Mobile Element 6: Bottom right */}
            <div className="absolute top-[323px] right-[-24px] w-[88px] h-[90px]">
              <Image src="/remix/bg-image-4-41e4b7.png" alt="" fill className="object-cover" />
            </div>
          </div>
        </div>

        {/* Content Container */}
        <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-10 space-y-6 lg:space-y-8">

          {/* Hero Section */}
          <div className="text-center space-y-4 lg:space-y-8">
            {/* Join Badge */}
            <div className="inline-block bg-[#FFD2F1] rounded-md px-3 py-1 lg:px-4 lg:py-1">
              <span className="text-foreground font-semibold text-xl lg:text-2xl font-lato">Join</span>
            </div>

            {/* Contest Title */}
            <h2 className="text-3xl lg:text-5xl font-bold text-foreground leading-tight font-arvo">
              Open Verse Contest
            </h2>

            {/* Artist Info */}
            <div className="flex items-center justify-center gap-2 lg:gap-3">
              <div className="w-8 h-8 lg:w-9 lg:h-9 rounded-full overflow-hidden">
                <Image src="/remix/figma-assets/kesha-avatar.png" alt="Kesha" width={36} height={36} className="object-cover" />
              </div>
              <span className="text-muted-foreground font-normal text-lg lg:text-xl font-arvo">By Kesha</span>
            </div>
          </div>

          {/* Main Hero Image with Pink Circle */}
          <div className="flex justify-center px-4 lg:px-0">
            <div className="relative w-full max-w-[320px] lg:max-w-[431px] aspect-square">
              <Image
                src="/remix/figma-assets/main-contest-image.png"
                alt="Contest Image"
                fill
                className="object-cover rounded-lg"
              />
              {/* Pink circle overlay - positioned as per Figma */}
              <div className="absolute top-[33%] left-[34%] w-[32%] h-[32%] bg-primary rounded-full"></div>
            </div>
          </div>

          {/* Large ATTENTION Title */}
          <div className="text-center">
            <h1 className="text-[60px] lg:text-[80px] font-bold text-primary leading-[1.5] lg:leading-[2] font-arvo">
              ATTENTION
            </h1>
          </div>

          {/* Download Message */}
          {downloadMessage && (
            <div className="text-center">
              <div className="inline-block bg-green-500/10 text-green-600 px-4 py-2 rounded-lg text-sm font-medium">
                {downloadMessage}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col lg:flex-row justify-center items-center gap-3 lg:gap-4 px-4 lg:px-0">
            <Button
              onClick={handlePlay}
              className="w-full lg:w-auto bg-primary text-primary-foreground border border-primary rounded-full px-8 lg:px-9 py-3 lg:py-4 font-bold text-sm lg:text-base uppercase font-arvo hover:bg-primary/90 flex items-center justify-center gap-2"
            >
              {currentSong?.id === remixSong.id && isPlaying ? (
                <>
                  <Pause className="w-4 h-4" />
                  PAUSE
                </>
              ) : (
                <>
                  <Play className="w-4 h-4" />
                  PLAY
                </>
              )}
            </Button>
            <Button
              onClick={handleDownload}
              disabled={isDownloading}
              className="w-full lg:w-auto bg-transparent text-primary border border-primary rounded-full px-8 lg:px-12 py-3 lg:py-4 font-bold text-sm lg:text-base uppercase font-arvo hover:bg-primary/5 flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isDownloading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Download className="w-4 h-4" />
              )}
              <span className="hidden sm:inline">
                {isDownloading ? 'DOWNLOADING...' : 'DOWNLOAD OPEN VERSE'}
              </span>
              <span className="sm:hidden">
                {isDownloading ? 'DOWNLOADING...' : 'DOWNLOAD'}
              </span>
            </Button>
          </div>
         
         

          {/* Content Sections */}
          <div className="space-y-3 lg:space-y-4">

            {/* Kesha's Message Section */}
            <section className="bg-card rounded-lg px-3 lg:px-4 py-4 lg:py-5">
              <h3 className="text-lg lg:text-[22px] font-bold text-card-foreground font-arvo mb-2 lg:mb-3">Kesha&apos;s Message</h3>
              <div className="text-card-foreground text-sm lg:text-base leading-5 lg:leading-6 font-inter space-y-3 lg:space-y-4">
                <h4 className="text-lg lg:text-2xl font-bold leading-normal  text-primary">Do I have your ATTENTION!?</h4>
                <p>It&apos;s official: I&apos;ve created another banger with my new song ATTENTION; featuring Slayyyter, and Rose Gray. But now it&apos;s time to pass the mic and bring ATTENTION TO YOU: <br/>  YOUR VOICE, <br/>  YOUR SCENE, <br/>  YOUR MESSAGE </p>
                <p>That&apos;s right I&apos;m sharing a version of ATTENTION with an open verse so you can give this song your own twist and bring ATTENTION to whatever you&apos;re passionate about. Go wild and let&apos;s make a SMASH together. Music knows no borders so I&apos;m excited to hear what you cook up from all over the world!</p>
                <p>And I&apos;m going to make sure the Artists behind the winning features get proper credit, ownership, and get paid! After all that&apos;s the whole point of my new music creation community SMASH. Keep in mind at SMASH and Kesha Records we like to keep the vibes positive and nurture a safe and supportive community where all creators feel safety to play - so let&apos;s keep these features free of hate!</p>
              </div>
            </section>

            {/* How to Participate Section */}
            <section className="bg-card rounded-lg px-3 lg:px-4 py-4 lg:py-5">
              <h3 className="text-lg lg:text-[22px] font-bold text-primary font-arvo uppercase mb-3 lg:mb-4">HOW TO PARTICIPATE</h3>
              <div className="space-y-2">

                {/* Step 1: Sign into SMASH */}
                <div className="flex gap-3 lg:gap-4">
                  <div className="flex flex-col items-center flex-shrink-0 w-6 lg:w-8">
                    <Download className="w-5 h-5 lg:w-6 lg:h-6 " />
                    <div className="w-0.5 h-6 lg:h-8 bg-primary mt-2"></div>
                  </div>
                  <div className="flex-1 pt-0.5">
                    <h4 className="text-sm lg:text-base font-bold text-card-foreground font-arvo mb-1">Sign into SMASH</h4>
                    <p className="text-muted-foreground text-sm lg:text-base font-lato leading-5 lg:leading-6">Join the community of music creators</p>
                  </div>
                </div>

                {/* Step 2: Download Open Verse */}
                <div className="flex gap-3 lg:gap-4">
                  <div className="flex flex-col items-center flex-shrink-0 w-6 lg:w-8">
                    <div className="w-0.5 h-2 bg-primary"></div>
                    <Download className="w-5 h-5 lg:w-6 lg:h-6 " />
                    <div className="w-0.5 h-6 lg:h-8 bg-primary mt-2"></div>
                  </div>
                  <div className="flex-1 pt-0.5">
                    <h4 className="text-sm lg:text-base font-bold text-card-foreground font-arvo mb-1">Download Open Verse</h4>
                    <p className="text-muted-foreground text-sm lg:text-base font-lato leading-5 lg:leading-6">Download a version of Attention with an open verse</p>
                  </div>
                </div>

                {/* Step 3: Create Your Verse */}
                <div className="flex gap-3 lg:gap-4">
                  <div className="flex flex-col items-center flex-shrink-0 w-6 lg:w-8">
                    <div className="w-0.5 h-2 bg-primary"></div>
                    <Music className="w-5 h-5 lg:w-6 lg:h-6 " />
                    <div className="w-0.5 h-6 lg:h-8 bg-primary mt-2"></div>
                  </div>
                  <div className="flex-1 pt-0.5">
                    <h4 className="text-sm lg:text-base font-bold text-card-foreground font-arvo mb-1">Create Your Verse</h4>
                    <p className="text-muted-foreground text-sm lg:text-base font-lato leading-5 lg:leading-6">SMASH It! Record your own unique verse on ATTENTION - don&apos;t hold back because only a few winners will be chosen</p>
                  </div>
                </div>

                {/* Step 4: Submit Your SMASH */}
                <div className="flex gap-3 lg:gap-4">
                  <div className="flex flex-col items-center flex-shrink-0 w-6 lg:w-8">
                    <div className="w-0.5 h-2 bg-primary"></div>
                    <Upload className="w-5 h-5 lg:w-6 lg:h-6 " />
                  </div>
                  <div className="flex-1 pt-0.5">
                    <h4 className="text-sm lg:text-base font-bold text-card-foreground font-arvo mb-1">Submit Your SMASH</h4>
                    <p className="text-muted-foreground text-sm lg:text-base font-lato leading-5 lg:leading-6">Upload your recording and share a video on socials tagging SMASH and Kesha to let us know you submitted</p>
                  </div>
                </div>
              </div>
            </section>

            {/* Prize Section */}
            <section className="bg-card rounded-lg px-3 lg:px-4 py-4 lg:py-5">
              <div className="text-card-foreground text-sm lg:text-base leading-5 lg:leading-6 font-inter space-y-3 lg:space-y-4">
                <h4 className="font-bold text-primary text-base lg:text-lg">PRIZE</h4>
                <p>Kesha will choose a few of the feature submissions to release officially via Kesha Records with the featured artist prominently credited. She will share the new composition across all DSPs as well as Kesha&apos;s own socials and the socials of SMASH and Kesha Records giving your voice the ATTENTION it deserves, introducing the featured artist to a wide audience and gaining valuable exposure.</p>
                <p>Featured artist will get 20% share ownership of the song (Publisher and Master) plus a $2,000 cash fee.</p>
              </div>
            </section>

            {/* Deadline Section */}
            <section className="bg-card rounded-lg px-3 lg:px-4 py-4 lg:py-5">
              <div className="text-card-foreground text-sm lg:text-base leading-5 lg:leading-6 font-inter space-y-3 lg:space-y-4">
                <h4 className="font-bold text-primary text-base lg:text-lg">DEADLINE</h4>
                <p>All features must be uploaded to SMASH by August 25nd.</p>
                <p>Together we will remake a more fair music business… one SMASH at a time!</p>
                <p>– Kesha</p>
              </div>
            </section>

            {/* Submission Guidelines Section */}
            <section className="bg-card rounded-lg px-3 lg:px-4 py-4 lg:py-5">
              <div className="space-y-4 lg:space-y-5">
                <div className="text-card-foreground text-sm lg:text-base leading-5 lg:leading-6 font-inter space-y-3 lg:space-y-4">
                  <h4 className="font-bold text-primary text-base lg:text-lg">SUBMISSION GUIDELINES</h4>
                  <p>By submitting your original work in the correct format and on time, you agree to follow all Smash Music contest rules and allow use of your submission for promotion. Only one entry per participant is allowed. Non-compliance may result in disqualification.</p>
                </div>

                {/* Checkbox */}
                <div className="flex items-start gap-2 lg:gap-3 pt-2 lg:pt-3">
                  <Checkbox
                    id="terms-agreement"
                    checked={isAgreed}
                    onCheckedChange={(checked) => setIsAgreed(checked as boolean)}
                    className="mt-0.5 lg:mt-1 w-4 h-4 lg:w-5 lg:h-5 border-primary data-[state=checked]:bg-primary data-[state=checked]:border-primary data-[state=checked]:text-primary-foreground"
                  />
                  <label htmlFor="terms-agreement" className="text-sm lg:text-base text-card-foreground cursor-pointer font-semibold font-lato leading-5 lg:leading-6">
                    I have read and agree to the{' '}
                    <button
                      type="button"
                      onClick={() => setShowDisclaimerModal(true)}
                      className="text-card-foreground font-extrabold hover:text-primary/80"
                    >
                      Terms & Conditions
                    </button>
                    {' '}of the contest.
                  </label>
                </div>
              </div>
            </section>

            {/* Bottom Action Buttons */}
            <section className="text-center pt-4 lg:pt-6 px-4 lg:px-0">
              <div className="flex flex-col lg:flex-row justify-center items-center gap-3 lg:gap-4">
                <Button
                  onClick={handleDownload}
                  disabled={isDownloading}
                  className="w-full lg:w-auto bg-transparent text-primary border border-primary rounded-full px-8 lg:px-12 py-3 lg:py-4 font-bold text-sm lg:text-base uppercase font-arvo hover:bg-primary/5 flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isDownloading ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Download className="w-4 h-4" />
                  )}
                  <span className="hidden sm:inline">
                    {isDownloading ? 'DOWNLOADING...' : 'DOWNLOAD OPEN VERSE'}
                  </span>
                  <span className="sm:hidden">
                    {isDownloading ? 'DOWNLOADING...' : 'DOWNLOAD'}
                  </span>
                </Button>
                <Button
                  onClick={handleSubmit}
                  disabled={!isAgreed}
                  className="w-full lg:w-auto bg-primary text-primary-foreground border border-primary rounded-full px-8 lg:px-12 py-3 lg:py-4 font-bold text-sm lg:text-base uppercase font-arvo hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                >
                  <Send className="w-4 h-4" />
                  SUBMIT TRACK
                </Button>
              </div>
            </section>
          </div>

          {/* Footer */}
          <footer className="text-center py-6 mt-8">
            <p className="text-foreground text-xl font-semibold font-lato">By Smash Music</p>
          </footer>
           </div>
      </main>

      {/* File Upload Modal */}
      <FileUploadModal
        isOpen={showUploadModal}
        onClose={handleCloseModal}
        onSubmit={handleUploadComplete}
      />

      {/* Disclaimer Modal */}
      <DisclaimerModal />
    </div>
  );
}

export default function RemixPage() {
  return <RemixPageContent />;
}
