/**
 * Custom React Hook for S3 Image Upload
 * Provides a clean API for components to handle image uploads with state management
 */

import { useState, useCallback, useRef } from 'react';
import { uploadImageToS3, validateImageFile } from '@/lib/s3-image-upload';
import type {
  ImageUploadState,
  ImageUploadResult,
  ImageUploadOptions,
  ImageValidationResult,
} from '@/types/s3-upload';

export interface UseS3ImageUploadReturn {
  // State
  uploadState: ImageUploadState;
  
  // Actions
  uploadImage: (file: File, options?: ImageUploadOptions) => Promise<ImageUploadResult>;
  validateImage: (file: File) => Promise<ImageValidationResult>;
  clearUpload: () => void;
  setPreviewUrl: (url: string | null) => void;
  
  // Computed values
  isUploading: boolean;
  hasError: boolean;
  hasUploadedImage: boolean;
  canUpload: boolean;
}

/**
 * Hook for managing S3 image uploads
 */
export function useS3ImageUpload(): UseS3ImageUploadReturn {
  const [uploadState, setUploadState] = useState<ImageUploadState>({
    isUploading: false,
    progress: 0,
    error: null,
    uploadedUrl: null,
    previewUrl: null,
  });

  // Keep track of current upload to prevent concurrent uploads
  const currentUploadRef = useRef<AbortController | null>(null);

  /**
   * Upload an image file to S3
   */
  const uploadImage = useCallback(async (
    file: File,
    options: ImageUploadOptions = {}
  ): Promise<ImageUploadResult> => {
    // Prevent concurrent uploads
    if (uploadState.isUploading) {
      return {
        success: false,
        error: 'Upload already in progress',
      };
    }

    // Cancel any existing upload
    if (currentUploadRef.current) {
      currentUploadRef.current.abort();
    }

    // Create new abort controller for this upload
    currentUploadRef.current = new AbortController();

    // Reset state and start upload
    setUploadState(prev => ({
      ...prev,
      isUploading: true,
      progress: 0,
      error: null,
      uploadedUrl: null,
    }));

    try {
      // Create preview URL immediately for better UX
      const previewUrl = URL.createObjectURL(file);
      setUploadState(prev => ({
        ...prev,
        previewUrl,
      }));

      // Upload with progress tracking
      const result = await uploadImageToS3(file, {
        ...options,
        onProgress: (progress) => {
          setUploadState(prev => ({
            ...prev,
            progress,
          }));
          options.onProgress?.(progress);
        },
      });

      if (result.success && result.url) {
        // Upload successful
        setUploadState(prev => ({
          ...prev,
          isUploading: false,
          progress: 100,
          uploadedUrl: result.url!,
          error: null,
        }));

        // Clean up preview URL since we now have the S3 URL
        URL.revokeObjectURL(previewUrl);
        setUploadState(prev => ({
          ...prev,
          previewUrl: null,
        }));
      } else {
        // Upload failed
        setUploadState(prev => ({
          ...prev,
          isUploading: false,
          progress: 0,
          error: result.error || 'Upload failed',
        }));
      }

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';

      setUploadState(prev => ({
        ...prev,
        isUploading: false,
        progress: 0,
        error: errorMessage,
      }));

      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      currentUploadRef.current = null;
    }
  }, [uploadState.isUploading]);

  /**
   * Validate an image file without uploading
   */
  const validateImage = useCallback(async (file: File): Promise<ImageValidationResult> => {
    try {
      return await validateImageFile(file);
    } catch (error) {
      return {
        isValid: false,
        error: error instanceof Error ? error.message : 'Validation failed',
      };
    }
  }, []);

  /**
   * Clear upload state and cleanup URLs
   */
  const clearUpload = useCallback(() => {
    // Cancel any ongoing upload
    if (currentUploadRef.current) {
      currentUploadRef.current.abort();
      currentUploadRef.current = null;
    }

    // Cleanup preview URL
    if (uploadState.previewUrl) {
      URL.revokeObjectURL(uploadState.previewUrl);
    }

    setUploadState({
      isUploading: false,
      progress: 0,
      error: null,
      uploadedUrl: null,
      previewUrl: null,
    });
  }, [uploadState.previewUrl]);

  /**
   * Set preview URL (for local file preview before upload)
   */
  const setPreviewUrl = useCallback((url: string | null) => {
    // Cleanup existing preview URL
    if (uploadState.previewUrl && uploadState.previewUrl !== url) {
      URL.revokeObjectURL(uploadState.previewUrl);
    }

    setUploadState(prev => ({
      ...prev,
      previewUrl: url,
    }));
  }, [uploadState.previewUrl]);

  // Computed values
  const isUploading = uploadState.isUploading;
  const hasError = uploadState.error !== null;
  const hasUploadedImage = uploadState.uploadedUrl !== null;
  const canUpload = !isUploading;

  return {
    uploadState,
    uploadImage,
    validateImage,
    clearUpload,
    setPreviewUrl,
    isUploading,
    hasError,
    hasUploadedImage,
    canUpload,
  };
}


