/**
 * S3 Upload Types and Interfaces
 * Defines TypeScript interfaces for S3 pre-signed URL uploads
 */

// API Request/Response Types
export interface S3PreSignedUrlRequest {
  filename: string;
}

export interface S3PreSignedUrlResponse {
  statusCode: number;
  body: string; // JSON string that needs to be parsed
}

export interface S3PreSignedUrlBody {
  uploadUrl: string;
  viewUrl: string;
  objectKey: string;
}

// Image Upload Configuration
export interface ImageUploadConfig {
  maxSizeBytes: number;
  maxWidth: number;
  maxHeight: number;
  minWidth: number;
  minHeight: number;
  allowedTypes: string[];
  allowedExtensions: string[];
}

// Image Validation Result
export interface ImageValidationResult {
  isValid: boolean;
  error?: string;
  warnings?: string[];
}

// Upload State Management
export interface ImageUploadState {
  isUploading: boolean;
  progress: number;
  error: string | null;
  uploadedUrl: string | null;
  previewUrl: string | null;
}

// Upload Result
export interface ImageUploadResult {
  success: boolean;
  url?: string;
  error?: string;
  objectKey?: string;
}

// Upload Progress Callback
export type UploadProgressCallback = (progress: number) => void;

// Upload Options
export interface ImageUploadOptions {
  onProgress?: UploadProgressCallback;
}

// Default Configuration
export const DEFAULT_IMAGE_UPLOAD_CONFIG: ImageUploadConfig = {
  maxSizeBytes: 5 * 1024 * 1024, // 5MB
  maxWidth: 2048,
  maxHeight: 2048,
  minWidth: 100,
  minHeight: 100,
  allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'],
  allowedExtensions: ['.jpg', '.jpeg', '.png', '.webp', '.gif'],
};

// API Configuration
export const S3_UPLOAD_API_CONFIG = {
  endpoint: 'https://57k50sc89a.execute-api.us-east-2.amazonaws.com/staging/generate-signed-url',
  filePathPrefix: 'users/example_name/',
} as const;
