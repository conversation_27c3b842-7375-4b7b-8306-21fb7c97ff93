'use client';

import React from 'react';
import { getBuildConfig } from '@/lib/build-config';
import { remixThemeConfig } from '@/app/remix/theme-config';

interface AuthThemeWrapperProps {
  children: React.ReactNode;
}

/**
 * Theme wrapper for authentication pages
 *
 * Behavior:
 * - When NEXT_PUBLIC_BUILD_MODE=remix: Applies remix theme (pink primary colors)
 * - When NEXT_PUBLIC_BUILD_MODE=main: Uses default theme (no wrapper applied)
 *
 * This ensures the login flow matches the app's current mode:
 * - Remix mode: Pink themed login/signup/forgot password pages
 * - Artist mode: Default themed login/signup/forgot password pages
 */
export function AuthThemeWrapper({ children }: AuthThemeWrapperProps) {
  // const { resolvedTheme } = useTheme();
  const config = getBuildConfig();

  // If in remix mode, apply remix theme
  if (config.mode === 'remix') {
        // const mode = resolvedTheme === 'dark' ? 'dark' : 'light';
         // Force light theme mode for remix flow
    const mode = 'light';
    const themeColors = remixThemeConfig[mode];

    const cssVariables: React.CSSProperties = {
      '--primary': `hsl(${themeColors.primary.DEFAULT})`,
      '--primary-foreground': `hsl(${themeColors.primary.foreground})`,
      '--secondary': `hsl(${themeColors.secondary.DEFAULT})`,
      '--secondary-foreground': `hsl(${themeColors.secondary.foreground})`,
      '--muted': `hsl(${themeColors.muted.DEFAULT})`,
      '--muted-foreground': `hsl(${themeColors.muted.foreground})`,
      '--accent': `hsl(${themeColors.accent.DEFAULT})`,
      '--accent-foreground': `hsl(${themeColors.accent.foreground})`,
      '--background': `hsl(${themeColors.background})`,
      '--foreground': `hsl(${themeColors.foreground})`,
      '--card': `hsl(${themeColors.card.DEFAULT})`,
      '--card-foreground': `hsl(${themeColors.card.foreground})`,
      '--border': `hsl(${themeColors.border})`,
      '--input': `hsl(${themeColors.input})`,
      '--ring': `hsl(${themeColors.ring})`,
      '--destructive': `hsl(${themeColors.destructive.DEFAULT})`,
      '--destructive-foreground': `hsl(${themeColors.destructive.foreground})`,
    } as React.CSSProperties;

    return (
      <div
        className="auth-remix-theme-scope"
        style={cssVariables}
        data-theme={mode}
      >
        {children}
      </div>
    );
  }

  // For artist mode, use default theme (no wrapper needed)
  return <>{children}</>;
}
