'use client';

import React from 'react';

interface MusicPlayerDarkThemeWrapperProps {
  children: React.ReactNode;
}

/**
 * Dark theme wrapper for music player
 * 
 * Forces dark theme for the music player component regardless of global theme settings.
 * This ensures the music player always has a consistent dark appearance.
 */
export function MusicPlayerDarkThemeWrapper({ children }: MusicPlayerDarkThemeWrapperProps) {
  // Dark theme CSS variables for music player
  const darkThemeVariables: React.CSSProperties = {
    // Background and foreground
    '--background': 'hsl(222.2 84% 4.9%)', // Dark background
    '--foreground': 'hsl(210 40% 98%)', // Light text
    
    // Card colors
    '--card': 'hsl(222.2 84% 4.9%)', // Dark cards
    '--card-foreground': 'hsl(210 40% 98%)', // Light text
    
    // Muted colors
    '--muted': 'hsl(217.2 32.6% 17.5%)', // Dark muted background
    '--muted-foreground': 'hsl(215 20.2% 65.1%)', // Light muted text
    
    // Border and input
    '--border': 'hsl(217.2 32.6% 17.5%)', // Dark border
    '--input': 'hsl(217.2 32.6% 17.5%)', // Dark input background
    
    // Primary colors (keeping default for buttons)
    '--primary': 'hsl(210 40% 98%)', // Light primary for dark theme
    '--primary-foreground': 'hsl(222.2 84% 4.9%)', // Dark text on light primary
    
    // Secondary colors
    '--secondary': 'hsl(217.2 32.6% 17.5%)', // Dark secondary
    '--secondary-foreground': 'hsl(210 40% 98%)', // Light text on dark secondary
    
    // Accent colors
    '--accent': 'hsl(217.2 32.6% 17.5%)', // Dark accent
    '--accent-foreground': 'hsl(210 40% 98%)', // Light text on dark accent
    
    // Ring (focus outline)
    '--ring': 'hsl(212.7 26.8% 83.9%)', // Light ring for dark theme
    
    // Destructive colors
    '--destructive': 'hsl(0 62.8% 30.6%)', // Dark red
    '--destructive-foreground': 'hsl(210 40% 98%)', // Light text
  } as React.CSSProperties;

  return (
    <div
      className="music-player-dark-theme"
      style={darkThemeVariables}
      data-theme="dark"
    >
      {children}
    </div>
  );
}
