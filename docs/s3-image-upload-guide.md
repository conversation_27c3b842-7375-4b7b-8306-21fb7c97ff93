# S3 Image Upload System - Usage Guide

A comprehensive, reusable image upload system for uploading images to AWS S3 using pre-signed URLs.

## 📋 Table of Contents

- [Overview](#overview)
- [Quick Start](#quick-start)
- [API Reference](#api-reference)
- [Components](#components)
- [Examples](#examples)
- [Configuration](#configuration)
- [Error Handling](#error-handling)
- [Best Practices](#best-practices)

## 🎯 Overview

The S3 Image Upload System provides:
- **Secure uploads** using AWS S3 pre-signed URLs
- **Real-time progress tracking** with progress bars
- **Image validation** (type, size, dimensions)
- **Immediate preview** with local blob URLs
- **Error handling** with user-friendly messages
- **TypeScript support** with full type safety

## 🚀 Quick Start

### 1. Import the Hook

```typescript
import { useS3ImageUpload } from '@/hooks/use-s3-image-upload';
```

### 2. Basic Usage

```typescript
export default function MyComponent() {
  const {
    uploadState,
    uploadImage,
    isUploading,
    hasError,
    hasUploadedImage,
  } = useS3ImageUpload();

  const handleFileSelect = async (file: File) => {
    const result = await uploadImage(file);
    if (result.success) {
      console.log('Upload successful:', result.url);
    }
  };

  return (
    <div>
      <input
        type="file"
        accept="image/*"
        onChange={(e) => {
          const file = e.target.files?.[0];
          if (file) handleFileSelect(file);
        }}
      />
      {isUploading && <p>Uploading... {uploadState.progress}%</p>}
      {hasError && <p>Error: {uploadState.error}</p>}
      {uploadState.uploadedUrl && (
        <img src={uploadState.uploadedUrl} alt="Uploaded" />
      )}
    </div>
  );
}
```

### 3. Advanced Usage with Preview

```typescript
export default function AdvancedUploader() {
  const {
    uploadState,
    uploadImage,
    validateImage,
    setPreviewUrl,
    isUploading,
    hasError,
  } = useS3ImageUpload();

  const handleFileSelect = async (file: File) => {
    // Validate first
    const validation = await validateImage(file);
    if (!validation.isValid) {
      alert(validation.error);
      return;
    }

    // Create immediate preview
    const previewUrl = URL.createObjectURL(file);
    setPreviewUrl(previewUrl);

    // Upload to S3
    const result = await uploadImage(file);
    if (result.success) {
      // Preview will be automatically replaced with S3 URL
      console.log('Upload successful:', result.url);
    }
  };

  // Get display URL (S3 URL or local preview)
  const displayUrl = uploadState.uploadedUrl || uploadState.previewUrl;

  return (
    <div>
      <input type="file" accept="image/*" onChange={handleFileSelect} />
      {displayUrl && <img src={displayUrl} alt="Preview" />}
      {isUploading && <p>Uploading... {uploadState.progress}%</p>}
      {hasError && <p>Error: {uploadState.error}</p>}
    </div>
  );
}
```

## 📚 API Reference

### Hook: `useS3ImageUpload()`

The single, comprehensive hook for all S3 image upload needs.

#### State Properties
- `uploadState: ImageUploadState` - Complete upload state object
  - `isUploading: boolean` - Upload in progress
  - `progress: number` - Upload progress (0-100)
  - `error: string | null` - Error message if any
  - `uploadedUrl: string | null` - S3 URL after successful upload
  - `previewUrl: string | null` - Local preview URL
- `isUploading: boolean` - Whether upload is in progress
- `hasError: boolean` - Whether there's an error
- `hasUploadedImage: boolean` - Whether image was successfully uploaded
- `canUpload: boolean` - Whether upload can be started

#### Methods
- `uploadImage(file: File, options?: ImageUploadOptions): Promise<ImageUploadResult>`
- `validateImage(file: File): Promise<ImageValidationResult>`
- `clearUpload(): void` - Clear current upload state and cleanup URLs
- `setPreviewUrl(url: string | null): void` - Set local preview URL

#### Helper Pattern for Display URL
```typescript
const { uploadState } = useS3ImageUpload();
const displayUrl = uploadState.uploadedUrl || uploadState.previewUrl;
```



### Utility Functions

```typescript
import {
  validateImageFile,
  uploadImageToS3,
  generateS3Filename,
} from '@/lib/s3-image-upload';

// Validate image without uploading
const validation = await validateImageFile(file);

// Direct upload (without hook)
const result = await uploadImageToS3(file, {
  onProgress: (progress) => console.log(`${progress}%`)
});
```

## 🧩 Components

### Basic Upload Component

```typescript
import React from 'react';
import { useS3ImageUpload } from '@/hooks/use-s3-image-upload';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';

export function ImageUploader({ onUploadComplete }: { 
  onUploadComplete: (url: string) => void 
}) {
  const { uploadState, uploadImage, isUploading, hasError } = useS3ImageUpload();

  const handleFileSelect = async (file: File) => {
    const result = await uploadImage(file);
    if (result.success && result.url) {
      onUploadComplete(result.url);
    }
  };

  return (
    <div className="space-y-4">
      <input
        type="file"
        accept="image/*"
        onChange={(e) => {
          const file = e.target.files?.[0];
          if (file) handleFileSelect(file);
        }}
        disabled={isUploading}
      />
      
      {isUploading && (
        <Progress value={uploadState.progress} className="w-full" />
      )}
      
      {hasError && (
        <p className="text-red-500 text-sm">{uploadState.error}</p>
      )}
    </div>
  );
}
```

### Drag & Drop Upload Component

```typescript
import React, { useState } from 'react';
import { useS3ImageUpload } from '@/hooks/use-s3-image-upload';

export function DragDropUploader() {
  const [isDragOver, setIsDragOver] = useState(false);
  const { uploadImage, isUploading, uploadState } = useS3ImageUpload();

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const file = e.dataTransfer.files[0];
    if (file && file.type.startsWith('image/')) {
      await uploadImage(file);
    }
  };

  return (
    <div
      className={`border-2 border-dashed rounded-lg p-8 text-center ${
        isDragOver ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
      }`}
      onDrop={handleDrop}
      onDragOver={(e) => {
        e.preventDefault();
        setIsDragOver(true);
      }}
      onDragLeave={() => setIsDragOver(false)}
    >
      {isUploading ? (
        <div>
          <p>Uploading... {uploadState.progress}%</p>
          <Progress value={uploadState.progress} className="mt-2" />
        </div>
      ) : (
        <p>Drag and drop an image here, or click to select</p>
      )}
    </div>
  );
}
```

## ⚙️ Configuration

### Image Validation Settings

```typescript
import { DEFAULT_IMAGE_UPLOAD_CONFIG } from '@/types/s3-upload';

// Default configuration:
{
  maxSizeBytes: 5 * 1024 * 1024, // 5MB
  maxWidth: 2048,
  maxHeight: 2048,
  minWidth: 100,
  minHeight: 100,
  allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'],
  allowedExtensions: ['.jpg', '.jpeg', '.png', '.webp', '.gif'],
}
```

### API Configuration

```typescript
import { S3_UPLOAD_API_CONFIG } from '@/types/s3-upload';

// API settings:
{
  endpoint: 'https://57k50sc89a.execute-api.us-east-2.amazonaws.com/staging/generate-signed-url',
  filePathPrefix: 'users/example_name/',
}
```

## 🚨 Error Handling

### Common Error Scenarios

```typescript
const handleUpload = async (file: File) => {
  try {
    const result = await uploadImage(file);
    
    if (!result.success) {
      switch (result.error) {
        case 'Please select an image file':
          // Handle invalid file type
          break;
        case 'Image size must be less than 5MB':
          // Handle file too large
          break;
        case 'API request failed':
          // Handle API errors
          break;
        case 'S3 upload failed':
          // Handle S3 upload errors
          break;
        default:
          // Handle other errors
          console.error('Upload failed:', result.error);
      }
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
};
```

### Error Recovery

```typescript
const { uploadState, uploadImage, clearUpload } = useS3ImageUpload();

// Retry upload
const handleRetry = async () => {
  if (uploadState.error && lastFile) {
    clearUpload(); // Clear error state
    await uploadImage(lastFile);
  }
};
```

## ✅ Best Practices

### 1. Always Validate Before Upload

```typescript
const handleFileSelect = async (file: File) => {
  // Validate first
  const validation = await validateImage(file);
  if (!validation.isValid) {
    alert(validation.error);
    return;
  }
  
  // Then upload
  await uploadImage(file);
};
```

### 2. Provide User Feedback

```typescript
// Show progress
{isUploading && <Progress value={uploadState.progress} />}

// Show errors
{hasError && <Alert>{uploadState.error}</Alert>}

// Show success
{hasUploadedImage && <CheckIcon />}
```

### 3. Handle Memory Management

```typescript
useEffect(() => {
  // Cleanup on unmount
  return () => {
    if (uploadState.previewUrl) {
      URL.revokeObjectURL(uploadState.previewUrl);
    }
  };
}, [uploadState.previewUrl]);
```

### 4. Disable UI During Upload

```typescript
<Button disabled={isUploading}>
  {isUploading ? 'Uploading...' : 'Upload Image'}
</Button>
```

### 5. Use TypeScript Types

```typescript
import type {
  ImageUploadResult,
  ImageUploadState,
  ImageValidationResult,
} from '@/types/s3-upload';
```

## 🔗 Integration Examples

### Form Integration

```typescript
const [imageUrl, setImageUrl] = useState<string>('');
const { uploadImage } = useS3ImageUpload();

const handleSubmit = async (formData: FormData) => {
  // Include uploaded image URL in form submission
  const data = {
    ...formData,
    profileImage: imageUrl,
  };
  
  await submitForm(data);
};
```

### Multiple Images

```typescript
const [uploadedImages, setUploadedImages] = useState<string[]>([]);

const handleMultipleUploads = async (files: FileList) => {
  const uploadPromises = Array.from(files).map(file => uploadImage(file));
  const results = await Promise.all(uploadPromises);
  
  const successfulUploads = results
    .filter(result => result.success)
    .map(result => result.url!);
    
  setUploadedImages(prev => [...prev, ...successfulUploads]);
};
```

---

## 📞 Support

For issues or questions about the S3 Image Upload System:
1. Check the error messages for specific guidance
2. Verify your API endpoint is accessible
3. Ensure your AWS S3 bucket has proper CORS configuration
4. Review the TypeScript types for proper usage

The system is designed to be robust and user-friendly, with comprehensive error handling and clear feedback for all scenarios.

---

## 🎯 Summary

**One Hook, All Use Cases**: `useS3ImageUpload()` is the single, comprehensive hook that handles:
- ✅ **Simple uploads** - Just call `uploadImage(file)`
- ✅ **Advanced workflows** - Full control with validation, previews, and progress
- ✅ **Error handling** - Built-in error states and recovery
- ✅ **Memory management** - Automatic cleanup of blob URLs
- ✅ **TypeScript support** - Full type safety

**Key Benefits**:
- **Consistent API** - Same hook across all components
- **Flexible** - Supports both simple and complex use cases
- **Production Ready** - Used in onboarding flows
- **Well Tested** - Comprehensive error handling and edge cases
